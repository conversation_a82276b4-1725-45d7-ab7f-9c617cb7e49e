#!/usr/bin/env python3
"""
简化版批量生成抽奖二维码脚本
修复了异步数据库连接问题
"""
import asyncio
import os
import sys
import argparse
import csv
import json
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app.config.database import AsyncSessionLocal
from app.services.lottery_code_service import LotteryCodeService
from app.schemas.lottery_code import LotteryCodeBatchCreate
from app.utils.logger import get_logger

logger = get_logger(__name__)


async def generate_lottery_codes(count: int, **config):
    """生成抽奖二维码的主函数"""
    print(f"🎯 开始生成 {count} 个抽奖二维码...")

    # 设置默认配置
    base_url = config.get("base_url", "https://activity.weilongmeiwei.com")  # 改为 FastAPI 默认端口
    url_template = config.get("url_template")

    if not url_template:
        # 使用 FastAPI 的抽奖页面路径
        url_template = f"{base_url}?code={{code}}"

    # 设置奖品分配
    prize_levels = config.get("prize_levels")
    if not prize_levels:
        # 默认分配：90%谢谢参与，8%三等奖，1.5%二等奖，0.5%一等奖
        no_prize = int(count * 0.90)
        third_prize = int(count * 0.08)
        second_prize = int(count * 0.015)
        first_prize = count - no_prize - third_prize - second_prize

        prize_levels = ([0] * no_prize + [3] * third_prize +
                        [2] * second_prize + [1] * first_prize)

    # 确保数量匹配
    while len(prize_levels) < count:
        prize_levels.append(0)
    if len(prize_levels) > count:
        prize_levels = prize_levels[:count]

    # 显示奖品分配
    print("📋 奖品分配:")
    prize_stats = {}
    for level in prize_levels:
        prize_stats[level] = prize_stats.get(level, 0) + 1

    for level in sorted(prize_stats.keys()):
        count_level = prize_stats[level]
        prize_name = LotteryCodeService.get_prize_name(level)
        percentage = (count_level / count) * 100
        print(f"   {prize_name}: {count_level}个 ({percentage:.1f}%)")

    # 创建批量数据
    batch_data = LotteryCodeBatchCreate(
        count=count,
        url_template=url_template,
        prize_levels=prize_levels
    )

    start_time = datetime.now()

    try:
        # 使用正确的异步数据库会话
        async with AsyncSessionLocal() as session:
            print("📡 正在连接数据库...")

            # 批量创建二维码
            lottery_codes = await LotteryCodeService.batch_create_lottery_codes(
                session, batch_data
            )

            # 在会话关闭前将 SQLAlchemy 对象转换为字典
            # 使用模型的 to_dict 方法避免懒加载问题
            codes_data = []
            for code in lottery_codes:
                try:
                    # 使用模型自带的 to_dict 方法
                    code_dict = code.to_dict()
                    codes_data.append(code_dict)
                except Exception as e:
                    # 如果 to_dict 失败，手动构建字典
                    logger.warning(f"使用 to_dict 失败，手动构建: {e}")
                    code_dict = {
                        'id': code.id,
                        'code': code.code,
                        'url': code.url,
                        'prize_level': code.prize_level,
                        'is_used': code.is_used,
                        'created_at': None,  # 避免懒加载
                        'updated_at': None   # 避免懒加载
                    }
                    codes_data.append(code_dict)

            end_time = datetime.now()
            generation_time = (end_time - start_time).total_seconds()

            print(f"✅ 生成完成！")
            print(f"   实际生成数量: {len(codes_data)}")
            print(f"   生成耗时: {generation_time:.2f}秒")
            print(f"   平均速度: {len(codes_data) / generation_time:.1f}个/秒")

            return codes_data, {
                "total_generated": len(codes_data),
                "generation_time": generation_time,
                "prize_distribution": prize_stats,
                "start_time": start_time,
                "end_time": end_time
            }

    except Exception as e:
        print(f"❌ 生成失败: {str(e)}")
        logger.error(f"批量生成失败: {str(e)}", exc_info=True)
        raise


async def export_to_csv(codes, filename: str = None):
    """导出到CSV文件"""
    if not filename:
        filename = f"lottery_codes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

    print(f"📄 导出到CSV文件: {filename}")

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'code', 'url'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for code in codes:
            # 处理字典格式的数据
            if isinstance(code, dict):
                code_data = code
            else:
                # 兼容 SQLAlchemy 对象（如果还有的话）
                code_data = {
                    'id': code.id,
                    'code': code.code,
                    'url': code.url,
                    'prize_level': code.prize_level,
                    'is_used': code.is_used,
                    'created_at': code.created_at
                }

            # row = {
            #     'id': code_data['id'],
            #     'code': code_data['code'],
            #     'url': code_data['url'],
            #     'prize_level': code_data['prize_level'],
            #     'prize_name': LotteryCodeService.get_prize_name(code_data['prize_level']),
            #     'is_used': code_data['is_used'],
            #     'created_at': code_data['created_at'].isoformat() if code_data['created_at'] else None,
            #     'qr_code_url': f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={code_data['url']}"
            # }
            row = {
                'code': code_data['code'],
                'url': code_data['url']
            }
            writer.writerow(row)

    print(f"✅ CSV导出完成: {filename}")
    return filename


async def export_to_json(codes, stats, filename: str = None):
    """导出到JSON文件"""
    if not filename:
        filename = f"lottery_codes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

    print(f"📄 导出到JSON文件: {filename}")

    # 转换代码数据
    codes_data = []
    for code in codes:
        # 处理字典格式的数据
        if isinstance(code, dict):
            code_dict = code
        else:
            # 兼容 SQLAlchemy 对象（如果还有的话）
            code_dict = {
                'code': code.code,
                'url': code.url,
            }

        # code_data = {
        #     "id": code_dict['id'],
        #     "code": code_dict['code'],
        #     "url": code_dict['url'],
        #     "prize_level": code_dict['prize_level'],
        #     "prize_name": LotteryCodeService.get_prize_name(code_dict['prize_level']),
        #     "is_used": code_dict['is_used'],
        #     "created_at": code_dict['created_at'].isoformat() if code_dict['created_at'] else None,
        #     "qr_code_url": f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={code_dict['url']}"
        # }
        code_data = {
            "code": code_dict['code'],
            "url": code_dict['url']
        }
        codes_data.append(code_data)

    export_data = {
        "metadata": {
            "export_time": datetime.now().isoformat(),
            "total_codes": len(codes),
            "statistics": {
                "total_generated": stats["total_generated"],
                "generation_time": stats["generation_time"],
                "prize_distribution": stats["prize_distribution"],
                "start_time": stats["start_time"].isoformat(),
                "end_time": stats["end_time"].isoformat()
            }
        },
        "codes": codes_data
    }

    with open(filename, 'w', encoding='utf-8') as jsonfile:
        json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

    print(f"✅ JSON导出完成: {filename}")
    return filename


def parse_prize_levels(prize_str: str, total_count: int) -> list:
    """解析奖品等级配置"""
    if not prize_str:
        return None

    try:
        # 尝试解析JSON格式
        if prize_str.startswith('['):
            return json.loads(prize_str)

        # 解析比例格式: 0:0.9,1:0.05,2:0.03,3:0.02
        if ':' in prize_str:
            prize_levels = []
            parts = prize_str.split(',')
            for part in parts:
                level, ratio = part.split(':')
                level = int(level.strip())
                ratio = float(ratio.strip())
                count = int(total_count * ratio)
                prize_levels.extend([level] * count)
            return prize_levels

        # 解析简单格式: 0,0,1,2,0
        return [int(x.strip()) for x in prize_str.split(',')]

    except Exception as e:
        print(f"⚠️  奖品等级解析失败: {e}")
        return None


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="简化版批量生成抽奖二维码",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python scripts/simple_batch_generate.py 1000
  python scripts/simple_batch_generate.py 500 --base-url "https://mysite.com/lottery"
  python scripts/simple_batch_generate.py 100 --prize-levels "0:0.8,1:0.1,2:0.1"
  python scripts/simple_batch_generate.py 50 --export-csv codes.csv --export-json codes.json
        """
    )

    parser.add_argument("count", type=int, help="生成数量")
    parser.add_argument("--base-url", help="基础URL")
    parser.add_argument("--url-template", help="URL模板 (必须包含{code})")
    parser.add_argument("--prize-levels", help="奖品等级配置")
    parser.add_argument("--export-csv", help="导出CSV文件名")
    parser.add_argument("--export-json", help="导出JSON文件名")

    args = parser.parse_args()

    # 验证参数
    if args.count <= 0:
        print("❌ 生成数量必须大于0")
        return

    if args.count > 10000:
        confirm = input(f"⚠️  您要生成 {args.count} 个二维码，这可能需要较长时间。确认继续？(y/N): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return

    # 准备配置
    config = {}
    if args.base_url:
        config["base_url"] = args.base_url
    if args.url_template:
        config["url_template"] = args.url_template
    if args.prize_levels:
        prize_levels = parse_prize_levels(args.prize_levels, args.count)
        if prize_levels:
            config["prize_levels"] = prize_levels

    print("🎰 简化版抽奖二维码批量生成器")
    print("=" * 50)
    print(f"生成数量: {args.count}")
    print(f"配置信息: {config}")
    print("=" * 50)

    try:
        # 生成二维码
        codes, stats = await generate_lottery_codes(args.count, **config)

        # 导出文件
        exported_files = []
        if args.export_csv:
            csv_file = await export_to_csv(codes, args.export_csv)
            exported_files.append(csv_file)

        if args.export_json:
            json_file = await export_to_json(codes, stats, args.export_json)
            exported_files.append(json_file)

        # 如果没有指定导出文件，询问是否导出
        if not args.export_csv and not args.export_json:
            export_choice = input("\n是否导出生成的数据？(csv/json/both/n): ").lower()
            if export_choice in ['csv', 'both']:
                csv_file = await export_to_csv(codes)
                exported_files.append(csv_file)
            if export_choice in ['json', 'both']:
                json_file = await export_to_json(codes, stats)
                exported_files.append(json_file)

        print(f"\n🎉 批量生成完成！")
        print(f"   共生成 {len(codes)} 个抽奖二维码")
        if exported_files:
            print(f"   导出文件: {', '.join(exported_files)}")

    except Exception as e:
        print(f"❌ 生成过程中出现错误: {str(e)}")
        logger.error(f"批量生成失败: {str(e)}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
