#!/usr/bin/env python3
"""
简化版奖品等级赋值脚本
修复了异步数据库连接问题
"""
import asyncio
import os
import sys
import argparse
import csv
import random
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from app.config.database import AsyncSessionLocal
from app.services.lottery_code_service import LotteryCodeService
from app.models.lottery_code import LotteryCode
from app.utils.logger import get_logger
from sqlalchemy import select, and_, update

logger = get_logger(__name__)


async def get_codes_by_criteria(session, **criteria):
    """根据条件获取二维码列表"""
    query = select(LotteryCode)
    conditions = []
    
    # 根据使用状态筛选
    if "is_used" in criteria:
        conditions.append(LotteryCode.is_used == criteria["is_used"])
    
    # 根据当前奖品等级筛选
    if "current_prize_level" in criteria:
        level = criteria["current_prize_level"]
        if isinstance(level, list):
            conditions.append(LotteryCode.prize_level.in_(level))
        else:
            conditions.append(LotteryCode.prize_level == level)
    
    # 根据ID范围筛选
    if "id_min" in criteria and criteria["id_min"]:
        conditions.append(LotteryCode.id >= criteria["id_min"])
    if "id_max" in criteria and criteria["id_max"]:
        conditions.append(LotteryCode.id <= criteria["id_max"])
    
    # 应用条件
    if conditions:
        query = query.where(and_(*conditions))
    
    # 添加限制和排序
    if "limit" in criteria:
        query = query.limit(criteria["limit"])
    
    query = query.order_by(LotteryCode.created_at.desc())
    
    result = await session.execute(query)
    return list(result.scalars().all())


async def assign_random_prizes(target_level: int, count: int, **criteria):
    """随机分配奖品等级"""
    print(f"🎯 随机选择 {count} 个二维码设为 {LotteryCodeService.get_prize_name(target_level)}")
    
    assignment_log = []
    
    try:
        async with AsyncSessionLocal() as session:
            # 获取符合条件的二维码
            codes = await get_codes_by_criteria(session, **criteria)
            
            if len(codes) < count:
                print(f"⚠️  符合条件的二维码数量({len(codes)})少于请求数量({count})")
                count = len(codes)
            
            if count == 0:
                print("❌ 没有符合条件的二维码")
                return 0, []
            
            # 随机选择
            selected_codes = random.sample(codes, count)
            
            # 批量更新
            updated_count = 0
            for code in selected_codes:
                old_level = code.prize_level
                code.prize_level = target_level
                
                assignment_log.append({
                    "id": code.id,
                    "code": code.code,
                    "old_level": old_level,
                    "new_level": target_level,
                    "old_prize_name": LotteryCodeService.get_prize_name(old_level),
                    "new_prize_name": LotteryCodeService.get_prize_name(target_level)
                })
                updated_count += 1
            
            await session.commit()
            print(f"✅ 随机分配完成，更新了 {updated_count} 个二维码")
            return updated_count, assignment_log
            
    except Exception as e:
        print(f"❌ 随机分配失败: {str(e)}")
        logger.error(f"随机分配失败: {str(e)}", exc_info=True)
        raise


async def assign_percentage_prizes(target_level: int, percentage: float, **criteria):
    """按百分比分配奖品等级"""
    print(f"🎯 按 {percentage*100:.1f}% 比例设为 {LotteryCodeService.get_prize_name(target_level)}")
    
    assignment_log = []
    
    try:
        async with AsyncSessionLocal() as session:
            # 获取符合条件的二维码
            codes = await get_codes_by_criteria(session, **criteria)
            total_count = len(codes)
            target_count = int(total_count * percentage)
            
            print(f"   总数: {total_count}, 目标更新数: {target_count}")
            
            if target_count == 0:
                print("❌ 计算出的目标数量为0")
                return 0, []
            
            # 随机选择指定百分比的二维码
            selected_codes = random.sample(codes, min(target_count, total_count))
            
            # 批量更新
            updated_count = 0
            for code in selected_codes:
                old_level = code.prize_level
                code.prize_level = target_level
                
                assignment_log.append({
                    "id": code.id,
                    "code": code.code,
                    "old_level": old_level,
                    "new_level": target_level,
                    "old_prize_name": LotteryCodeService.get_prize_name(old_level),
                    "new_prize_name": LotteryCodeService.get_prize_name(target_level)
                })
                updated_count += 1
            
            await session.commit()
            print(f"✅ 按百分比分配完成，更新了 {updated_count} 个二维码")
            return updated_count, assignment_log
            
    except Exception as e:
        print(f"❌ 按百分比分配失败: {str(e)}")
        logger.error(f"按百分比分配失败: {str(e)}", exc_info=True)
        raise


async def assign_specific_ids(target_level: int, ids: list):
    """指定ID分配奖品等级"""
    print(f"🎯 为指定的 {len(ids)} 个ID设为 {LotteryCodeService.get_prize_name(target_level)}")
    
    assignment_log = []
    
    try:
        async with AsyncSessionLocal() as session:
            # 查询指定ID的二维码
            query = select(LotteryCode).where(LotteryCode.id.in_(ids))
            result = await session.execute(query)
            codes = list(result.scalars().all())
            
            if len(codes) != len(ids):
                print(f"⚠️  找到 {len(codes)} 个二维码，请求 {len(ids)} 个ID")
            
            # 批量更新
            updated_count = 0
            for code in codes:
                old_level = code.prize_level
                code.prize_level = target_level
                
                assignment_log.append({
                    "id": code.id,
                    "code": code.code,
                    "old_level": old_level,
                    "new_level": target_level,
                    "old_prize_name": LotteryCodeService.get_prize_name(old_level),
                    "new_prize_name": LotteryCodeService.get_prize_name(target_level)
                })
                updated_count += 1
            
            await session.commit()
            print(f"✅ 指定ID分配完成，更新了 {updated_count} 个二维码")
            return updated_count, assignment_log
            
    except Exception as e:
        print(f"❌ 指定ID分配失败: {str(e)}")
        logger.error(f"指定ID分配失败: {str(e)}", exc_info=True)
        raise


async def assign_specific_codes(target_level: int, codes_list: list):
    """指定编码分配奖品等级"""
    print(f"🎯 为指定的 {len(codes_list)} 个编码设为 {LotteryCodeService.get_prize_name(target_level)}")
    
    assignment_log = []
    
    try:
        async with AsyncSessionLocal() as session:
            # 查询指定编码的二维码
            query = select(LotteryCode).where(LotteryCode.code.in_(codes_list))
            result = await session.execute(query)
            codes = list(result.scalars().all())
            
            if len(codes) != len(codes_list):
                print(f"⚠️  找到 {len(codes)} 个二维码，请求 {len(codes_list)} 个编码")
            
            # 批量更新
            updated_count = 0
            for code in codes:
                old_level = code.prize_level
                code.prize_level = target_level
                
                assignment_log.append({
                    "id": code.id,
                    "code": code.code,
                    "old_level": old_level,
                    "new_level": target_level,
                    "old_prize_name": LotteryCodeService.get_prize_name(old_level),
                    "new_prize_name": LotteryCodeService.get_prize_name(target_level)
                })
                updated_count += 1
            
            await session.commit()
            print(f"✅ 指定编码分配完成，更新了 {updated_count} 个二维码")
            return updated_count, assignment_log
            
    except Exception as e:
        print(f"❌ 指定编码分配失败: {str(e)}")
        logger.error(f"指定编码分配失败: {str(e)}", exc_info=True)
        raise


async def preview_assignment(strategy: str, **kwargs):
    """预览分配结果"""
    print("📋 预览分配结果...")
    
    try:
        async with AsyncSessionLocal() as session:
            if strategy in ["random", "percentage"]:
                criteria = {k: v for k, v in kwargs.items() 
                           if k in ["is_used", "current_prize_level", "id_min", "id_max", "limit"]}
                codes = await get_codes_by_criteria(session, **criteria)
                total_count = len(codes)
                
                if strategy == "random":
                    will_update = min(kwargs.get("count", 0), total_count)
                else:  # percentage
                    will_update = int(total_count * kwargs.get("percentage", 0))
                
            elif strategy == "specific_ids":
                total_count = len(kwargs.get("ids", []))
                will_update = total_count
                
            elif strategy == "specific_codes":
                total_count = len(kwargs.get("codes", []))
                will_update = total_count
            
            print(f"   符合条件的总数: {total_count}")
            print(f"   将要更新的数量: {will_update}")
            
            return total_count, will_update
            
    except Exception as e:
        print(f"❌ 预览失败: {str(e)}")
        return 0, 0


async def export_assignment_log(assignment_log, filename: str = None):
    """导出分配日志"""
    if not assignment_log:
        print("❌ 没有分配日志可导出")
        return
    
    if not filename:
        filename = f"prize_assignment_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    print(f"📄 导出分配日志到: {filename}")
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['id', 'code', 'old_level', 'old_prize_name', 'new_level', 'new_prize_name', 'timestamp']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for log in assignment_log:
            row = {
                'id': log['id'],
                'code': log['code'],
                'old_level': log['old_level'],
                'old_prize_name': log['old_prize_name'],
                'new_level': log['new_level'],
                'new_prize_name': log['new_prize_name'],
                'timestamp': datetime.now().isoformat()
            }
            writer.writerow(row)
    
    print(f"✅ 日志导出完成: {filename}")
    return filename


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="简化版奖品等级赋值工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python scripts/simple_assign_prizes.py 1 --strategy random --count 100
  python scripts/simple_assign_prizes.py 2 --strategy percentage --percentage 0.05 --unused-only
  python scripts/simple_assign_prizes.py 1 --strategy ids --ids "123,456,789"
  python scripts/simple_assign_prizes.py 3 --strategy codes --codes "abc123,def456"
        """
    )
    
    parser.add_argument("prize_level", type=int, help="目标奖品等级")
    parser.add_argument("--strategy", required=True, 
                       choices=["random", "percentage", "ids", "codes"],
                       help="分配策略")
    parser.add_argument("--count", type=int, help="数量 (用于random策略)")
    parser.add_argument("--percentage", type=float, help="百分比 (用于percentage策略)")
    parser.add_argument("--ids", help="ID列表，逗号分隔 (用于ids策略)")
    parser.add_argument("--codes", help="编码列表，逗号分隔 (用于codes策略)")
    parser.add_argument("--unused-only", action="store_true", help="只处理未使用的二维码")
    parser.add_argument("--current-level", type=int, help="只处理指定当前等级的二维码")
    parser.add_argument("--id-range", help="ID范围，格式: min:max")
    parser.add_argument("--preview", action="store_true", help="仅预览，不实际执行")
    parser.add_argument("--export-log", help="导出分配日志文件名")
    
    args = parser.parse_args()
    
    # 验证参数
    if args.prize_level < 0:
        print("❌ 奖品等级不能为负数")
        return
    
    # 准备筛选条件
    criteria = {}
    if args.unused_only:
        criteria["is_used"] = False
    if args.current_level is not None:
        criteria["current_prize_level"] = args.current_level
    if args.id_range:
        try:
            min_id, max_id = args.id_range.split(':')
            criteria["id_min"] = int(min_id) if min_id else None
            criteria["id_max"] = int(max_id) if max_id else None
        except ValueError:
            print("❌ ID范围格式错误，应为 min:max")
            return
    
    # 准备策略参数
    strategy_kwargs = criteria.copy()
    
    if args.strategy == "random":
        if not args.count:
            print("❌ random策略需要指定--count参数")
            return
        strategy_kwargs["count"] = args.count
        
    elif args.strategy == "percentage":
        if not args.percentage:
            print("❌ percentage策略需要指定--percentage参数")
            return
        strategy_kwargs["percentage"] = args.percentage
        
    elif args.strategy == "ids":
        if not args.ids:
            print("❌ ids策略需要指定--ids参数")
            return
        strategy_kwargs["ids"] = [int(id.strip()) for id in args.ids.split(',')]
        
    elif args.strategy == "codes":
        if not args.codes:
            print("❌ codes策略需要指定--codes参数")
            return
        strategy_kwargs["codes"] = [code.strip() for code in args.codes.split(',')]
    
    print("🎰 简化版奖品等级赋值工具")
    print("=" * 50)
    print(f"目标等级: {args.prize_level} ({LotteryCodeService.get_prize_name(args.prize_level)})")
    print(f"分配策略: {args.strategy}")
    print(f"筛选条件: {criteria}")
    print("=" * 50)
    
    try:
        # 预览模式
        if args.preview:
            await preview_assignment(args.strategy, **strategy_kwargs)
            return
        
        # 确认执行
        if (args.strategy == "random" and args.count > 100) or \
           (args.strategy == "percentage" and args.percentage > 0.1):
            confirm = input("⚠️  这将更新较多数据，确认继续？(y/N): ")
            if confirm.lower() != 'y':
                print("操作已取消")
                return
        
        # 执行分配
        updated_count = 0
        assignment_log = []
        
        if args.strategy == "random":
            updated_count, assignment_log = await assign_random_prizes(
                args.prize_level, **strategy_kwargs
            )
        elif args.strategy == "percentage":
            updated_count, assignment_log = await assign_percentage_prizes(
                args.prize_level, **strategy_kwargs
            )
        elif args.strategy == "ids":
            updated_count, assignment_log = await assign_specific_ids(
                args.prize_level, strategy_kwargs["ids"]
            )
        elif args.strategy == "codes":
            updated_count, assignment_log = await assign_specific_codes(
                args.prize_level, strategy_kwargs["codes"]
            )
        
        # 打印统计信息
        print(f"\n📊 分配统计:")
        print(f"   更新记录数: {updated_count}")
        
        if assignment_log:
            # 统计变更详情
            change_stats = {}
            for log in assignment_log:
                change = f"{log['old_prize_name']} -> {log['new_prize_name']}"
                change_stats[change] = change_stats.get(change, 0) + 1
            
            print(f"   变更详情:")
            for change, count in change_stats.items():
                print(f"     {change}: {count}个")
        
        # 导出日志
        if args.export_log:
            await export_assignment_log(assignment_log, args.export_log)
        elif updated_count > 0:
            export_choice = input("\n是否导出分配日志？(y/N): ")
            if export_choice.lower() == 'y':
                await export_assignment_log(assignment_log)
        
        print(f"\n🎉 奖品等级赋值完成！共更新 {updated_count} 个二维码")
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {str(e)}")
        logger.error(f"奖品等级赋值失败: {str(e)}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
