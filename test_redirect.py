#!/usr/bin/env python3
"""
测试重定向参数保留功能
"""

import requests
import time

def test_redirect_with_params():
    """测试带参数的重定向"""
    
    base_url = "http://localhost:8000"
    
    # 等待服务重启
    print("等待服务重启...")
    time.sleep(3)
    
    test_cases = [
        {
            "name": "带 code 参数",
            "url": "/?code=a1b2c3d4e5f6789012345678901234ab",
            "expected_redirect": "/lottery?code=a1b2c3d4e5f6789012345678901234ab"
        },
        {
            "name": "带多个参数",
            "url": "/?code=test123&source=qr&campaign=summer",
            "expected_redirect": "/lottery?code=test123&source=qr&campaign=summer"
        },
        {
            "name": "无参数",
            "url": "/",
            "expected_redirect": "/lottery"
        },
        {
            "name": "空参数",
            "url": "/?",
            "expected_redirect": "/lottery"
        }
    ]
    
    print("=== 重定向参数保留测试 ===\n")
    
    for case in test_cases:
        print(f"测试: {case['name']}")
        print(f"请求 URL: {base_url}{case['url']}")
        
        try:
            # 不跟随重定向，获取重定向信息
            response = requests.get(
                f"{base_url}{case['url']}", 
                allow_redirects=False,
                timeout=5
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 302:
                location = response.headers.get('location', '')
                print(f"重定向到: {location}")
                print(f"期望重定向: {case['expected_redirect']}")
                
                if location == case['expected_redirect']:
                    print("✅ 参数保留正确")
                else:
                    print("❌ 参数保留失败")
            else:
                print(f"❌ 期望 302 重定向，实际得到 {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
        
        print("-" * 50)
    
    # 测试完整流程（跟随重定向）
    print("\n=== 完整流程测试 ===")
    test_code = "a1b2c3d4e5f6789012345678901234ab"
    full_url = f"{base_url}/?code={test_code}"
    
    try:
        print(f"访问: {full_url}")
        response = requests.get(full_url, timeout=5)
        
        print(f"最终状态码: {response.status_code}")
        print(f"最终 URL: {response.url}")
        
        if response.status_code == 200:
            # 检查页面是否包含代码参数
            if test_code in response.text:
                print("✅ 页面包含代码参数，参数传递成功")
            else:
                print("⚠️ 页面不包含代码参数，可能参数未正确传递")
                
            # 检查页面标题
            if '<title>' in response.text:
                title_start = response.text.find('<title>') + 7
                title_end = response.text.find('</title>')
                title = response.text[title_start:title_end]
                print(f"页面标题: {title}")
        else:
            print(f"❌ 最终页面加载失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 完整流程测试失败: {e}")

if __name__ == "__main__":
    test_redirect_with_params()
