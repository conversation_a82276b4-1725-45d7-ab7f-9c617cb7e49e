<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="扫码兑奖 - 快速验证您的中奖信息">
    <meta name="theme-color" content="#4F46E5">
    <title>扫码兑奖</title>

    <!-- Preload critical resources -->
    <link rel="preload" href="https://ai-public.mastergo.com/ai/img_res/aea24a984fc7dcbaabbdd383d2fc784a.jpg" as="image">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#6366F1',
                        success: '#10B981',
                        warning: '#F59E0B',
                        error: '#EF4444'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '2px',
                        DEFAULT: '4px',
                        'md': '8px',
                        'lg': '12px',
                        'xl': '16px',
                        '2xl': '20px',
                        '3xl': '24px',
                        'full': '9999px',
                        'button': '8px'
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-gentle': 'bounceGentle 1s ease-in-out infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        bounceGentle: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        /* Custom loading spinner */
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #4F46E5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Smooth transitions */
        .transition-all-smooth {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Button hover effects */
        .btn-primary {
            background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%);
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        /* Accessibility improvements */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles for better accessibility */
        button:focus-visible,
        a:focus-visible {
            outline: 2px solid #4F46E5;
            outline-offset: 2px;
            border-radius: 4px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .bg-primary { background-color: #000000 !important; }
            .text-primary { color: #000000 !important; }
            .border-primary { border-color: #000000 !important; }
        }

        /* Reduced motion preferences */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            .spinner {
                animation: none;
                border: 4px solid #4F46E5;
                border-radius: 50%;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #111827;
                color: #f9fafb;
            }

            .bg-white {
                background-color: #1f2937 !important;
            }

            .text-gray-900 {
                color: #f9fafb !important;
            }

            .text-gray-600 {
                color: #d1d5db !important;
            }

            .text-gray-500 {
                color: #9ca3af !important;
            }

            .border-gray-100 {
                border-color: #374151 !important;
            }

            .shadow-sm {
                box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3) !important;
            }

            .shadow-lg {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3) !important;
            }
        }

        /* Print styles */
        @media print {
            .fixed, button {
                display: none !important;
            }

            body {
                background: white !important;
                color: black !important;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans antialiased">
    <!-- Header -->
    <header class="fixed top-0 w-full bg-white shadow-sm z-50 transition-all-smooth">
        <div class="h-[44px] flex items-center justify-center relative">
            <h1 class="text-base font-medium text-gray-900" role="banner">扫码兑奖</h1>
        </div>
    </header>

    <!-- Main Content -->
    <main class="min-h-screen pt-[60px] pb-[20px] px-4" role="main">
        <!-- Initial State -->
        <div id="initial" class="flex flex-col items-center justify-center text-center mb-8 animate-fade-in">
            <div class="w-full h-[220px] mb-6 bg-gradient-to-b from-primary/10 to-white rounded-xl flex items-center justify-center shadow-sm">
                <img src="https://ai-public.mastergo.com/ai/img_res/aea24a984fc7dcbaabbdd383d2fc784a.jpg"
                     class="w-[200px] h-[200px] object-contain transition-all-smooth hover:scale-105"
                     alt="扫码示意图"
                     loading="eager">
            </div>
            <h2 class="text-xl font-medium mb-3 text-gray-900">请扫描兑奖二维码</h2>
            <p class="text-gray-500 text-sm max-w-[280px] leading-relaxed">将您收到的兑奖二维码对准扫描框，即可验证中奖信息</p>
        </div>
        <!-- Loading State -->
        <div id="loading" class="hidden flex flex-col items-center justify-center animate-fade-in" role="status" aria-live="polite">
            <div class="w-16 h-16 mb-6 flex items-center justify-center">
                <div class="spinner"></div>
            </div>
            <h2 class="text-lg font-medium mb-2 text-gray-900">正在验证中</h2>
            <p class="text-gray-500 text-sm">请稍候，正在验证您的兑奖码...</p>
            <div class="mt-4 flex space-x-1">
                <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 0ms;"></div>
                <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 150ms;"></div>
                <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 300ms;"></div>
            </div>
        </div>

        <!-- Success State -->
        <div id="success" class="hidden flex flex-col items-center justify-center animate-slide-up" role="alert" aria-live="polite">
            <div class="w-24 h-24 mb-6 flex items-center justify-center bg-success/10 rounded-full animate-bounce-gentle">
                <i class="fas fa-check text-4xl text-success"></i>
            </div>
            <h2 class="text-2xl font-bold mb-2 text-gray-900">🎉 恭喜您中奖了！</h2>
            <p class="text-gray-600 mb-6 text-center" id="prize-description">您获得了「iPhone 15 Pro Max」</p>

            <div class="w-full max-w-[320px] p-6 bg-white rounded-2xl shadow-lg mb-8 border border-gray-100">
                <div class="flex items-center mb-4">
                    <img src="https://ai-public.mastergo.com/ai/img_res/7cfde5efa7e6b7beb23840b126c4bc96.jpg"
                         class="w-20 h-20 object-cover rounded-xl shadow-sm"
                         alt="奖品图片"
                         loading="lazy">
                    <div class="ml-4 flex-1">
                        <h3 class="font-semibold text-gray-900" id="prize-name">iPhone 15 Pro Max</h3>
                        <p class="text-sm text-gray-500 mt-1" id="prize-spec">256GB 原色钛金属</p>
                    </div>
                </div>
                <div class="border-t border-gray-100 pt-4 space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">兑奖编号</span>
                        <span class="font-mono text-gray-900" id="prize-code">WIN202312080001</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-500">有效期至</span>
                        <span class="text-gray-900" id="prize-expiry">2023-12-31 23:59:59</span>
                    </div>
                </div>
            </div>

            <button class="btn-primary w-full max-w-[280px] h-[48px] text-white font-medium rounded-button shadow-lg"
                    id="claim-btn"
                    aria-label="立即领取奖品">
                立即领取
            </button>
        </div>
        <!-- Fail State -->
        <div id="fail" class="hidden flex flex-col items-center justify-center animate-slide-up" role="alert" aria-live="polite">
            <div class="w-24 h-24 mb-6 flex items-center justify-center bg-gray-100 rounded-full">
                <i class="fas fa-gift text-4xl text-gray-400"></i>
            </div>
            <h2 class="text-xl font-medium mb-2 text-gray-900">很遗憾未中奖</h2>
            <p class="text-gray-600 mb-8 text-center max-w-[280px] leading-relaxed">
                别灰心，感谢您的参与！<br>
                下次一定会有好运气的 🍀
            </p>
            <button class="btn-primary w-full max-w-[280px] h-[48px] text-white font-medium rounded-button shadow-lg"
                    id="retry-btn"
                    aria-label="再试一次">
                再试一次
            </button>
        </div>

        <!-- Error State -->
        <div id="error" class="hidden flex flex-col items-center justify-center animate-slide-up" role="alert" aria-live="assertive">
            <div class="w-24 h-24 mb-6 flex items-center justify-center bg-error/10 rounded-full">
                <i class="fas fa-exclamation-triangle text-4xl text-error"></i>
            </div>
            <h2 class="text-xl font-medium mb-2 text-gray-900">验证失败</h2>
            <p class="text-gray-600 mb-8 text-center max-w-[280px] leading-relaxed" id="error-message">
                网络连接异常，请检查网络后重试
            </p>
            <button class="btn-primary w-full max-w-[280px] h-[48px] text-white font-medium rounded-button shadow-lg"
                    id="verify-btn"
                    aria-label="重新验证">
                重新验证
            </button>
        </div>

        <!-- Invalid Code State -->
        <div id="invalid" class="hidden flex flex-col items-center justify-center animate-slide-up" role="alert" aria-live="assertive">
            <div class="w-24 h-24 mb-6 flex items-center justify-center bg-warning/10 rounded-full">
                <i class="fas fa-qrcode text-4xl text-warning"></i>
            </div>
            <h2 class="text-xl font-medium mb-2 text-gray-900">兑奖码无效</h2>
            <p class="text-gray-600 mb-8 text-center max-w-[280px] leading-relaxed">
                请检查您的兑奖码是否正确，<br>
                或联系客服获取帮助
            </p>
            <div class="flex flex-col space-y-3 w-full max-w-[280px]">
                <button class="btn-primary h-[48px] text-white font-medium rounded-button shadow-lg"
                        id="contact-btn"
                        aria-label="联系客服">
                    联系客服
                </button>
                <button class="h-[48px] bg-gray-100 text-gray-700 font-medium rounded-button hover:bg-gray-200 transition-all-smooth"
                        id="back-btn"
                        aria-label="返回首页">
                    返回首页
                </button>
            </div>
        </div>
    </main>
    <!-- JavaScript -->
    <script>
        // Application state management
        class LotteryApp {
            constructor() {
                this.states = ['initial', 'loading', 'success', 'fail', 'error', 'invalid'];
                this.currentState = 'initial';
                this.retryCount = 0;
                this.maxRetries = 3;
                this.init();
            }

            init() {
                this.bindEvents();
                this.handleInitialLoad();
            }

            // Utility functions
            getQueryParam(name) {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(name);
            }

            showState(state, data = null) {
                // Hide all states
                this.states.forEach(s => {
                    const element = document.getElementById(s);
                    if (element) {
                        element.style.display = 'none';
                        element.classList.remove('animate-fade-in', 'animate-slide-up');
                    }
                });

                // Show target state with animation
                const targetElement = document.getElementById(state);
                if (targetElement) {
                    targetElement.style.display = 'flex';
                    // Trigger animation after a small delay
                    setTimeout(() => {
                        targetElement.classList.add(state === 'loading' ? 'animate-fade-in' : 'animate-slide-up');
                    }, 50);
                }

                this.currentState = state;

                // Update content based on data
                if (state === 'success' && data) {
                    this.updateSuccessContent(data);
                } else if (state === 'error' && data?.message) {
                    this.updateErrorMessage(data.message);
                }

                // Announce state change for screen readers
                this.announceStateChange(state);
            }

            updateSuccessContent(data) {
                const elements = {
                    'prize-description': data.description || '您获得了精美奖品',
                    'prize-name': data.name || '神秘奖品',
                    'prize-spec': data.specification || '详情请咨询客服',
                    'prize-code': data.code || 'N/A',
                    'prize-expiry': data.expiry || '请尽快领取'
                };

                Object.entries(elements).forEach(([id, content]) => {
                    const element = document.getElementById(id);
                    if (element) element.textContent = content;
                });
            }

            updateErrorMessage(message) {
                const errorElement = document.getElementById('error-message');
                if (errorElement) {
                    errorElement.textContent = message;
                }
            }

            announceStateChange(state) {
                const messages = {
                    loading: '正在验证兑奖码',
                    success: '验证成功，恭喜中奖',
                    fail: '验证完成，未中奖',
                    error: '验证失败，请重试',
                    invalid: '兑奖码无效'
                };

                const message = messages[state];
                if (message) {
                    // Create a temporary element for screen reader announcement
                    const announcement = document.createElement('div');
                    announcement.setAttribute('aria-live', 'polite');
                    announcement.setAttribute('aria-atomic', 'true');
                    announcement.className = 'sr-only';
                    announcement.textContent = message;
                    document.body.appendChild(announcement);

                    setTimeout(() => {
                        document.body.removeChild(announcement);
                    }, 1000);
                }
            }

            async verifyCode(code) {
                this.showState('loading');

                try {
                    console.log('开始验证代码:', code);

                    // 验证代码不为空
                    if (!code || code.trim() === '') {
                        throw new Error('代码不能为空');
                    }

                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

                    // 准备请求数据
                    const requestData = { code: code.trim() };
                    console.log('发送请求数据:', requestData);

                    // 调用后端扫码抽奖接口
                    const response = await fetch('http://172.29.52.142:8080/api/v1/lottery-codes/scan', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify(requestData),
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId);

                    console.log('响应状态:', response.status, response.statusText);
                    console.log('响应头:', Object.fromEntries(response.headers.entries()));

                    // 检查响应是否成功
                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('HTTP错误响应:', errorText);
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const result = await response.json();
                    console.log('API响应结果:', result);

                    // 处理API响应 - 后端使用统一响应格式
                    if (result.code === 200 && result.data) {
                        const scanData = result.data;

                        if (scanData.success) {
                            // 中奖了
                            const prizeData = {
                                description: `您获得了「${scanData.prize_name || '神秘奖品'}」`,
                                name: scanData.prize_name || '神秘奖品',
                                specification: this.getPrizeSpec(scanData.prize_level),
                                code: scanData.code,
                                expiry: this.getExpiryDate()
                            };
                            this.showState('success', prizeData);
                        } else {
                            // 未中奖
                            this.showState('fail');
                        }
                    } else if (result.code === 400) {
                        // 请求错误（如二维码已使用、不存在等）
                        const scanData = result.data;
                        if (scanData && scanData.message) {
                            if (scanData.message.includes('已使用') || scanData.message.includes('不存在')) {
                                this.showState('invalid');
                            } else {
                                this.showState('fail');
                            }
                        } else {
                            this.showState('invalid');
                        }
                    } else {
                        throw new Error(`API Error: ${result.message || 'Unknown error'}`);
                    }

                    this.retryCount = 0; // Reset retry count on successful request

                } catch (error) {
                    console.error('Verification error:', error);

                    let errorMessage = '网络连接异常，请检查网络后重试';

                    if (error.name === 'AbortError') {
                        errorMessage = '请求超时，请检查网络连接';
                    } else if (error.message.includes('Failed to fetch')) {
                        errorMessage = '无法连接到服务器，请检查网络';
                    } else if (error.message.includes('API Error')) {
                        errorMessage = error.message.replace('API Error: ', '');
                    }

                    this.showState('error', { message: errorMessage });
                }
            }

            // 获取奖品规格描述
            getPrizeSpec(prizeLevel) {
                const specs = {
                    0: '谢谢参与',
                    1: '一等奖',
                    2: '二等奖',
                    3: '三等奖',
                    4: '四等奖'
                };
                return specs[prizeLevel] || '详情请咨询客服';
            }

            // 获取有效期
            getExpiryDate() {
                const now = new Date();
                const expiry = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天后
                return expiry.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            }

            handleInitialLoad() {
                const code = this.getQueryParam('code');
                console.log('Received code:', code);

                if (!code || code.trim() === '') {
                    this.showState('invalid');
                    return;
                }

                // 验证代码格式 - 后端要求32位十六进制字符串（UUID去除-）
                const cleanCode = code.trim().toLowerCase();
                if (!/^[a-f0-9]{32}$/.test(cleanCode)) {
                    console.log('Invalid code format:', cleanCode);
                    this.showState('invalid');
                    return;
                }

                // Start verification
                this.verifyCode(cleanCode);
            }

            handleRetry() {
                if (this.retryCount >= this.maxRetries) {
                    this.showState('error', {
                        message: '重试次数过多，请稍后再试或联系客服'
                    });
                    return;
                }

                this.retryCount++;
                const code = this.getQueryParam('code');
                if (code) {
                    this.verifyCode(code.trim());
                }
            }

            bindEvents() {
                // Claim button
                document.addEventListener('click', (e) => {
                    if (e.target.id === 'claim-btn') {
                        e.preventDefault();
                        this.handleClaim();
                    } else if (e.target.id === 'retry-btn' || e.target.id === 'verify-btn') {
                        e.preventDefault();
                        this.handleRetry();
                    } else if (e.target.id === 'contact-btn') {
                        e.preventDefault();
                        this.handleContact();
                    } else if (e.target.id === 'back-btn') {
                        e.preventDefault();
                        this.handleBack();
                    }
                });

                // Keyboard navigation
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && e.target.tagName === 'BUTTON') {
                        e.target.click();
                    }
                });
            }

            handleClaim() {
                // Add loading state to button
                const claimBtn = document.getElementById('claim-btn');
                if (claimBtn) {
                    claimBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>跳转中...';
                    claimBtn.disabled = true;
                }

                // Redirect to claim page
                const code = this.getQueryParam('code');
                if (code) {
                    window.location.href = '/claim?code=' + encodeURIComponent(code);
                } else {
                    alert('中奖码丢失，请重新扫码');
                    window.location.href = '/lottery';
                }
            }

            handleContact() {
                // This could open a modal, redirect to contact page, or open a chat widget
                window.location.href = '/contact';
            }

            handleBack() {
                window.location.href = '/';
            }
        }

        // Initialize app when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.lotteryApp = new LotteryApp();
        });

        // Add error boundary for unhandled errors
        window.addEventListener('error', (e) => {
            console.error('Unhandled error:', e.error);
            if (window.lotteryApp) {
                window.lotteryApp.showState('error', {
                    message: '页面出现异常，请刷新重试'
                });
            }
        });

        // Handle network status changes
        window.addEventListener('online', () => {
            if (window.lotteryApp && window.lotteryApp.currentState === 'error') {
                // Auto-retry when network comes back online
                setTimeout(() => {
                    window.lotteryApp.handleRetry();
                }, 1000);
            }
        });

        window.addEventListener('offline', () => {
            if (window.lotteryApp) {
                window.lotteryApp.showState('error', {
                    message: '网络连接已断开，请检查网络连接'
                });
            }
        });
    </script>
</body>
</html>
