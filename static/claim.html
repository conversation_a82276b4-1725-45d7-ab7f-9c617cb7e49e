<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="领奖页面 - 填写收货信息">
    <meta name="theme-color" content="#4F46E5">
    <title>领取奖品</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#6366F1',
                        success: '#10B981',
                        warning: '#F59E0B',
                        error: '#EF4444'
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        .btn-primary {
            background: linear-gradient(135deg, #4F46E5 0%, #6366F1 100%);
            transition: all 0.2s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
        }
        
        .btn-primary:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        .form-input {
            transition: all 0.2s ease;
        }
        
        .form-input:focus {
            border-color: #4F46E5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
    </style>
</head>

<body class="bg-gray-50 font-sans antialiased">
    <!-- Header -->
    <header class="fixed top-0 w-full bg-white shadow-sm z-50">
        <div class="h-[44px] flex items-center justify-center relative">
            <button onclick="goBack()" class="absolute left-4 text-gray-600 hover:text-gray-900">
                <i class="fas fa-arrow-left text-lg"></i>
            </button>
            <h1 class="text-base font-medium text-gray-900">领取奖品</h1>
        </div>
    </header>

    <!-- Main Content -->
    <main class="min-h-screen pt-[60px] pb-[20px] px-4" role="main">
        <!-- Prize Info -->
        <div id="prizeInfo" class="bg-white rounded-2xl shadow-sm p-6 mb-6 animate-fade-in">
            <div class="text-center mb-4">
                <div class="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-gift text-2xl text-success"></i>
                </div>
                <h2 class="text-xl font-bold text-gray-900 mb-2">🎉 恭喜中奖！</h2>
                <p class="text-gray-600" id="prizeDescription">您获得了精美奖品</p>
            </div>
            
            <div class="bg-gray-50 rounded-xl p-4">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500">中奖码</span>
                    <span class="font-mono text-gray-900" id="prizeCode">-</span>
                </div>
            </div>
        </div>

        <!-- Claim Form -->
        <div id="claimForm" class="bg-white rounded-2xl shadow-sm p-6 animate-slide-up">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">请填写收货信息</h3>
            
            <form id="receiveForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user mr-1"></i>收货人姓名 *
                    </label>
                    <input type="text" id="userName" name="userName" required
                           class="form-input w-full p-3 border border-gray-300 rounded-lg focus:outline-none"
                           placeholder="请输入收货人姓名">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-phone mr-1"></i>手机号 *
                    </label>
                    <input type="tel" id="mobile" name="mobile" required
                           class="form-input w-full p-3 border border-gray-300 rounded-lg focus:outline-none"
                           placeholder="请输入手机号"
                           pattern="^1[3-9]\d{9}$">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-map-marker-alt mr-1"></i>收货地址 *
                    </label>
                    <textarea id="address" name="address" required rows="3"
                              class="form-input w-full p-3 border border-gray-300 rounded-lg focus:outline-none resize-none"
                              placeholder="请输入详细的收货地址"></textarea>
                </div>
                
                <div class="pt-4">
                    <button type="submit" id="submitBtn"
                            class="btn-primary w-full h-[48px] text-white font-medium rounded-lg">
                        <span id="submitText">提交领奖信息</span>
                        <i id="submitLoading" class="fas fa-spinner fa-spin ml-2 hidden"></i>
                    </button>
                </div>
            </form>
        </div>

        <!-- Success State -->
        <div id="successState" class="hidden bg-white rounded-2xl shadow-sm p-6 text-center animate-slide-up">
            <div class="w-20 h-20 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-3xl text-success"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">提交成功！</h3>
            <p class="text-gray-600 mb-6">您的领奖信息已提交成功，我们会尽快为您发货。</p>
            <button onclick="goBack()" 
                    class="btn-primary px-8 py-3 text-white font-medium rounded-lg">
                返回首页
            </button>
        </div>

        <!-- Error State -->
        <div id="errorState" class="hidden bg-white rounded-2xl shadow-sm p-6 text-center animate-slide-up">
            <div class="w-20 h-20 bg-error/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-exclamation-triangle text-3xl text-error"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-2">提交失败</h3>
            <p class="text-gray-600 mb-6" id="errorMessage">请稍后重试</p>
            <div class="flex space-x-3">
                <button onclick="showClaimForm()" 
                        class="btn-primary flex-1 py-3 text-white font-medium rounded-lg">
                    重新填写
                </button>
                <button onclick="goBack()" 
                        class="flex-1 py-3 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200">
                    返回首页
                </button>
            </div>
        </div>
    </main>

    <script>
        class ClaimApp {
            constructor() {
                this.code = this.getQueryParam('code');
                this.init();
            }

            init() {
                if (!this.code || !/^[a-f0-9]{32}$/.test(this.code)) {
                    this.showError('无效的中奖码');
                    return;
                }

                this.updatePrizeInfo();
                this.bindEvents();
            }

            getQueryParam(name) {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(name);
            }

            updatePrizeInfo() {
                document.getElementById('prizeCode').textContent = this.code;
                // 这里可以调用 API 获取奖品信息
            }

            bindEvents() {
                const form = document.getElementById('receiveForm');
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.submitForm();
                });

                // 手机号格式验证
                const mobileInput = document.getElementById('mobile');
                mobileInput.addEventListener('input', (e) => {
                    const value = e.target.value.replace(/\D/g, '');
                    e.target.value = value;
                });
            }

            async submitForm() {
                const submitBtn = document.getElementById('submitBtn');
                const submitText = document.getElementById('submitText');
                const submitLoading = document.getElementById('submitLoading');

                // 获取表单数据
                const formData = {
                    code: this.code,
                    user_name: document.getElementById('userName').value.trim(),
                    mobile: document.getElementById('mobile').value.trim(),
                    address: document.getElementById('address').value.trim()
                };

                // 验证表单
                if (!this.validateForm(formData)) {
                    return;
                }

                // 显示加载状态
                submitBtn.disabled = true;
                submitText.textContent = '提交中...';
                submitLoading.classList.remove('hidden');

                try {
                    const response = await fetch('/api/v1/prize-receive/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showSuccess();
                    } else {
                        this.showError(result.message || '提交失败，请重试');
                    }

                } catch (error) {
                    console.error('提交失败:', error);
                    this.showError('网络错误，请检查网络连接后重试');
                } finally {
                    // 恢复按钮状态
                    submitBtn.disabled = false;
                    submitText.textContent = '提交领奖信息';
                    submitLoading.classList.add('hidden');
                }
            }

            validateForm(data) {
                if (!data.user_name) {
                    alert('请输入收货人姓名');
                    return false;
                }

                if (!data.mobile || !/^1[3-9]\d{9}$/.test(data.mobile)) {
                    alert('请输入正确的手机号');
                    return false;
                }

                if (!data.address) {
                    alert('请输入收货地址');
                    return false;
                }

                return true;
            }

            showSuccess() {
                document.getElementById('prizeInfo').style.display = 'none';
                document.getElementById('claimForm').style.display = 'none';
                document.getElementById('errorState').style.display = 'none';
                document.getElementById('successState').style.display = 'block';
            }

            showError(message) {
                document.getElementById('errorMessage').textContent = message;
                document.getElementById('prizeInfo').style.display = 'none';
                document.getElementById('claimForm').style.display = 'none';
                document.getElementById('successState').style.display = 'none';
                document.getElementById('errorState').style.display = 'block';
            }

            showClaimForm() {
                document.getElementById('prizeInfo').style.display = 'block';
                document.getElementById('claimForm').style.display = 'block';
                document.getElementById('successState').style.display = 'none';
                document.getElementById('errorState').style.display = 'none';
            }
        }

        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/lottery';
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            window.claimApp = new ClaimApp();
        });
    </script>
</body>
</html>
