from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>, Request, Path
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from starlette.exceptions import HTTPException as StarletteHTTPException
import os
import pathlib

from app.config.settings import settings
from app.config.logging import setup_logging
from app.config.database import init_db, close_db
from app.core.exceptions import (
    APIException,
    api_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from app.core.middleware import LoggingMiddleware, SecurityHeadersMiddleware
from app.api import api_router
import logging


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info(f"正在启动 {settings.app.name} v{settings.app.version}")
    logger.info(f"运行环境: {settings.environment}")

    try:
        # 初始化数据库
        await init_db()
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.warning(f"数据库初始化失败: {str(e)}")
        logger.warning("应用将在无数据库模式下启动，部分功能可能不可用")

    logger.info("应用启动完成")

    yield

    # 关闭时执行
    logger.info("正在关闭应用...")
    try:
        await close_db()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"关闭数据库连接时出错: {str(e)}")

    logger.info("应用已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""

    app = FastAPI(
        title=settings.app.name,
        version=settings.app.version,
        description="Activity Lottery API - 活动抽奖系统",
        debug=settings.app.debug,
        lifespan=lifespan,
        docs_url="/docs" if settings.app.debug else None,
        redoc_url="/redoc" if settings.app.debug else None,
    )

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors.allow_origins,
        allow_credentials=settings.cors.allow_credentials,
        allow_methods=settings.cors.allow_methods,
        allow_headers=settings.cors.allow_headers,
    )

    # 添加自定义中间件
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(SecurityHeadersMiddleware)

    # 注册异常处理器
    app.add_exception_handler(APIException, api_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)

    # 注册路由
    app.include_router(api_router, prefix="/api/v1")

    # 挂载静态文件
    static_dir = "static"
    if os.path.exists(static_dir):
        app.mount("/static", StaticFiles(directory=static_dir), name="static")

    # 抽奖页面路由
    @app.get("/lottery", include_in_schema=False)
    async def lottery_page():
        """抽奖页面"""
        static_file = static_dir + "/index.html"
        parent_image_path = "../" + static_dir + "/index.html"
        if os.path.exists(static_file):
            return FileResponse(static_file, media_type="text/html")
        else:
            if os.path.exists(parent_image_path):
                return FileResponse(parent_image_path, media_type="text/html")
            from fastapi import HTTPException
            raise HTTPException(status_code=404, detail="抽奖页面不存在")

    @app.get("/images/{image}", summary='图片接口')
    async def image_router(image: str = Path(..., description="图片名称")):
        """图片路由"""
        image_path = f'{static_dir}/"images"/{image}'
        if os.path.exists(image_path):
            return FileResponse(image_path, media_type="image/jpeg")
        else:
            # 使用上级目录
            PROJECT_ROOT: pathlib.Path = pathlib.Path(__file__).resolve().parent
            ABSOLUTE_STATIC_PATH: Path = PROJECT_ROOT / static_dir
            image_full_path: Path = ABSOLUTE_STATIC_PATH / "images" / image
            if os.path.exists(image_full_path):
                return FileResponse(image_path, media_type="image/jpeg")
            else:
                from fastapi import HTTPException
                raise HTTPException(status_code=404, detail="图片不存在")
        pass

    # API 根路径
    @app.get("/api")
    async def api_root():
        from app.core.response import success_response
        return success_response(
            data={
                "service": settings.app.name,
                "version": settings.app.version,
                "environment": settings.environment,
                "docs": "/docs" if settings.app.debug else "disabled",
            },
            message="欢迎使用活动抽奖系统API"
        )

    # 根路径重定向到抽奖页面
    @app.get("/")
    async def root(request: Request):
        """根路径，重定向到抽奖页面，保留查询参数"""
        from fastapi.responses import RedirectResponse

        # 获取查询参数
        query_string = str(request.url.query)

        # 构建重定向 URL，保留参数
        redirect_url = "/lottery"
        if query_string:
            redirect_url = f"/lottery?{query_string}"

        return RedirectResponse(url=redirect_url)

    return app


# 创建应用实例
app = create_app()
