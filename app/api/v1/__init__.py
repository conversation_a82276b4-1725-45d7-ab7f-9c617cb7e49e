from fastapi import APIRouter
from .endpoints import health, lottery_codes, prize_receive

api_router = APIRouter()

# 注册路由
api_router.include_router(health.router, prefix="/health", tags=["健康检查"])
api_router.include_router(lottery_codes.router, prefix="/lottery-codes", tags=["抽奖二维码"])
api_router.include_router(prize_receive.router, prefix="/prize-receive", tags=["领奖信息"])

# 可以在这里添加更多路由
# api_router.include_router(activities.router, prefix="/activities", tags=["活动管理"])
