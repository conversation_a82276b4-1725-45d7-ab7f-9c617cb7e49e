from typing import Optional
from fastapi import APIRouter, Depends, Query, Path, Request
from sqlalchemy.ext.asyncio import AsyncSession
import math

from app.api.deps import get_database
from app.core.response import CommonResponse, ResponseModel
from app.core.exceptions import APIException
from app.schemas.prize_receive_info import (
    PrizeReceiveInfoCreate, PrizeReceiveInfoUpdate, PrizeReceiveInfoResponse,
    PrizeReceiveInfoList, PrizeReceiveInfoStats, PrizeReceiveInfoQuery,
    ShipmentUpdate, ReceiptConfirm
)
from app.services.prize_receive_service import PrizeReceiveService
from app.utils.logger import get_logger
from app.utils import get_client_ip
logger = get_logger(__name__)

router = APIRouter()


@router.post("/save", response_model=ResponseModel[PrizeReceiveInfoResponse], summary='保存收货信息')
async def create_prize_receive_info(
        request: Request,
        receive_data: PrizeReceiveInfoCreate,
        db: AsyncSession = Depends(get_database)
):
    """
    创建领奖信息
    
    - **code**: 中奖码（32位十六进制）
    - **user_name**: 收货人姓名
    - **mobile**: 手机号
    - **address**: 收货地址
    - **image_url**: 四等奖壁纸链接（可选）
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(request)
        prize_receive_info = await PrizeReceiveService.create_prize_receive_info(db, receive_data, client_ip)
        return CommonResponse.created(
            data=PrizeReceiveInfoResponse.model_validate(prize_receive_info),
            message="领奖信息提交成功"
        )
    except Exception as e:
        logger.error(f"创建领奖信息失败: {str(e)}")
        raise APIException(f"创建领奖信息失败: {str(e)}")


@router.get("/code/{code}", response_model=ResponseModel[PrizeReceiveInfoResponse], include_in_schema=False)
async def get_prize_receive_info_by_code(
        code: str = Path(..., description="中奖码"),
        db: AsyncSession = Depends(get_database)
):
    """
    根据中奖码获取领奖信息
    """
    try:
        prize_receive_info = await PrizeReceiveService.get_by_code(db, code)
        if not prize_receive_info:
            return CommonResponse.not_found("该中奖码暂无领奖信息")

        return CommonResponse.success(
            data=PrizeReceiveInfoResponse.model_validate(prize_receive_info)
        )
    except Exception as e:
        logger.error(f"获取领奖信息失败: {str(e)}")
        raise APIException(f"获取领奖信息失败: {str(e)}")


@router.put("/{receive_id}", response_model=ResponseModel[PrizeReceiveInfoResponse], include_in_schema=False)
async def update_prize_receive_info(
        receive_id: int = Path(..., description="领奖信息ID"),
        update_data: PrizeReceiveInfoUpdate = ...,
        db: AsyncSession = Depends(get_database)
):
    """
    更新领奖信息
    """
    try:
        prize_receive_info = await PrizeReceiveService.update_prize_receive_info(
            db, receive_id, update_data
        )
        return CommonResponse.updated(
            data=PrizeReceiveInfoResponse.model_validate(prize_receive_info),
            message="领奖信息更新成功"
        )
    except Exception as e:
        logger.error(f"更新领奖信息失败: {str(e)}")
        raise APIException(f"更新领奖信息失败: {str(e)}")


@router.post("/{receive_id}/ship", response_model=ResponseModel[PrizeReceiveInfoResponse], include_in_schema=False)
async def ship_prize(
        receive_id: int = Path(..., description="领奖信息ID"),
        db: AsyncSession = Depends(get_database)
):
    """
    发货
    """
    try:
        prize_receive_info = await PrizeReceiveService.ship_prize(db, receive_id)
        return CommonResponse.success(
            data=PrizeReceiveInfoResponse.model_validate(prize_receive_info),
            message="发货成功"
        )
    except Exception as e:
        logger.error(f"发货失败: {str(e)}")
        raise APIException(f"发货失败: {str(e)}")


@router.post("/{receive_id}/confirm", response_model=ResponseModel[PrizeReceiveInfoResponse], include_in_schema=False)
async def confirm_receipt(
        receive_id: int = Path(..., description="领奖信息ID"),
        db: AsyncSession = Depends(get_database)
):
    """
    确认收货
    """
    try:
        prize_receive_info = await PrizeReceiveService.confirm_receipt(db, receive_id)
        return CommonResponse.success(
            data=PrizeReceiveInfoResponse.model_validate(prize_receive_info),
            message="确认收货成功"
        )
    except Exception as e:
        logger.error(f"确认收货失败: {str(e)}")
        raise APIException(f"确认收货失败: {str(e)}")


@router.get("/", response_model=ResponseModel[PrizeReceiveInfoList], include_in_schema=False)
async def get_prize_receive_list(
        page: int = Query(1, ge=1, description="页码"),
        size: int = Query(20, ge=1, le=100, description="每页大小"),
        code: Optional[str] = Query(None, description="中奖码"),
        prize_level: Optional[int] = Query(None, ge=0, description="中奖等级"),
        status: Optional[int] = Query(None, ge=0, le=2, description="状态"),
        user_name: Optional[str] = Query(None, description="姓名"),
        mobile: Optional[str] = Query(None, description="手机号"),
        db: AsyncSession = Depends(get_database)
):
    """
    获取领奖信息列表
    
    - **page**: 页码（从1开始）
    - **size**: 每页大小（1-100）
    - **code**: 中奖码（模糊搜索）
    - **prize_level**: 中奖等级筛选
    - **status**: 状态筛选（0待发货，1已发货，2已收货）
    - **user_name**: 姓名（模糊搜索）
    - **mobile**: 手机号（模糊搜索）
    """
    try:
        query = PrizeReceiveInfoQuery(
            code=code,
            prize_level=prize_level,
            status=status,
            user_name=user_name,
            mobile=mobile
        )

        items, total = await PrizeReceiveService.get_prize_receive_list(db, query, page, size)

        # 转换为响应模型
        response_items = [
            PrizeReceiveInfoResponse.model_validate(item) for item in items
        ]

        pages = math.ceil(total / size) if total > 0 else 1

        result = PrizeReceiveInfoList(
            items=response_items,
            total=total,
            page=page,
            size=size,
            pages=pages
        )

        return CommonResponse.success(data=result)

    except Exception as e:
        logger.error(f"获取领奖信息列表失败: {str(e)}")
        raise APIException(f"获取领奖信息列表失败: {str(e)}")


@router.get("/stats/overview", response_model=ResponseModel[PrizeReceiveInfoStats], include_in_schema=False)
async def get_prize_receive_stats(
        db: AsyncSession = Depends(get_database)
):
    """
    获取领奖统计信息
    """
    try:
        stats = await PrizeReceiveService.get_statistics(db)

        result = PrizeReceiveInfoStats(
            total_prizes=stats["total_prizes"],
            pending_count=stats["pending_count"],
            shipped_count=stats["shipped_count"],
            received_count=stats["received_count"],
            prize_level_stats=stats["prize_level_stats"],
            status_stats=stats["status_stats"]
        )

        return CommonResponse.success(data=result)

    except Exception as e:
        logger.error(f"获取领奖统计失败: {str(e)}")
        raise APIException(f"获取领奖统计失败: {str(e)}")


@router.delete("/{receive_id}", response_model=ResponseModel[None], include_in_schema=False)
async def delete_prize_receive_info(
        receive_id: int = Path(..., description="领奖信息ID"),
        db: AsyncSession = Depends(get_database)
):
    """
    删除领奖信息
    """
    try:
        await PrizeReceiveService.delete_prize_receive_info(db, receive_id)
        return CommonResponse.deleted("领奖信息删除成功")
    except Exception as e:
        logger.error(f"删除领奖信息失败: {str(e)}")
        raise APIException(f"删除领奖信息失败: {str(e)}")
