from fastapi import APIRouter, Depends, Path, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_database
from app.core.exceptions import APIException
from app.core.response import CommonResponse, ResponseModel
from app.schemas.lottery_code import (
    LotteryCodeResponse,
    LotteryCodeScan, LotteryCodeScanResponse,
    LotteryCodeStats
)
from app.services.lottery_code_service import LotteryCodeService
from app.utils.logger import get_logger
from app.utils import get_client_ip

logger = get_logger(__name__)

router = APIRouter()


@router.post("/scan", response_model=ResponseModel, summary="扫码抽奖")
async def scan_lottery_code(
        scan_data: LotteryCodeScan,
        request: Request,
        db: AsyncSession = Depends(get_database)
):
    """扫码抽奖"""
    try:
        # 获取客户端IP
        client_ip = get_client_ip(request)

        # 执行扫码
        result = await LotteryCodeService.scan_lottery_code(db, scan_data.code, client_ip)

        # 构造响应
        scan_response = LotteryCodeScanResponse(**result)

        if result["success"]:
            return CommonResponse.success(data=scan_response.model_dump(), message=result["message"])
        else:
            return CommonResponse.bad_request(message=result["message"], data=scan_response.model_dump())

    except Exception as e:
        logger.error(f"扫码抽奖失败: {str(e)}")
        raise APIException(f"扫码抽奖失败: {str(e)}")


@router.get("/code/{code}", response_model=ResponseModel, summary="根据编码获取二维码信息")
async def get_lottery_code_by_code(
        code: str = Path(..., description="二维码编码"),
        db: AsyncSession = Depends(get_database)
):
    """根据编码获取抽奖二维码信息（不更新使用状态）"""
    try:
        lottery_code = await LotteryCodeService.get_code_by_code(db, code)
        if not lottery_code:
            return CommonResponse.not_found("抽奖二维码不存在")

        code_response = LotteryCodeResponse.model_validate(lottery_code)
        return CommonResponse.success(data=code_response.model_dump(), message="获取抽奖二维码成功")
    except Exception as e:
        logger.error(f"获取抽奖二维码失败: {str(e)}")
        raise APIException(f"获取抽奖二维码失败: {str(e)}")


@router.get("/stats/overview", response_model=ResponseModel, summary="获取抽奖统计信息",include_in_schema = False)
async def get_lottery_stats(
        db: AsyncSession = Depends(get_database)
):
    """获取抽奖统计信息"""
    try:
        stats = await LotteryCodeService.get_lottery_stats(db)
        stats_response = LotteryCodeStats(**stats)
        return CommonResponse.success(data=stats_response.model_dump(), message="获取统计信息成功")
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise APIException(f"获取统计信息失败: {str(e)}")
