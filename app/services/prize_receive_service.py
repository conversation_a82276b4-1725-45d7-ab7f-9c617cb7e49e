from typing import Optional, List, Tuple, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc
from datetime import datetime

from app.models.prize_receive_info import PrizeReceiveInfo
from app.models.lottery_code import LotteryCode
from app.schemas.prize_receive_info import (
    PrizeReceiveInfoCreate, PrizeReceiveInfoUpdate, PrizeReceiveInfoQuery
)
from app.core.exceptions import NotFoundException, ValidationException
from app.utils.snowflake import generate_snowflake_id
from app.utils.logger import get_logger

logger = get_logger(__name__)


class PrizeReceiveService:
    """领奖信息服务类"""
    
    @staticmethod
    async def create_prize_receive_info(
        db: AsyncSession, 
        receive_data: PrizeReceiveInfoCreate,
        ip_address: str
    ) -> PrizeReceiveInfo:
        """创建领奖信息"""
        
        # 验证中奖码是否存在且已中奖
        lottery_code = await PrizeReceiveService.get_lottery_code_by_code(db, receive_data.code)
        if not lottery_code:
            raise NotFoundException("中奖码不存在")
        
        if lottery_code.is_used:
            raise ValidationException("该中奖码已使用")
        
        if lottery_code.prize_level == 0:
            raise ValidationException("该中奖码未中奖，无法领取奖品")
        
        # 检查是否已经创建过领奖信息
        existing_info = await PrizeReceiveService.get_by_code(db, receive_data.code)
        if existing_info:
            raise ValidationException("该中奖码已经提交过领奖信息")

        
        # 生成雪花ID
        snowflake_id = generate_snowflake_id()
        
        # 创建领奖信息
        prize_receive_info = PrizeReceiveInfo(
            id=snowflake_id,
            code=receive_data.code,
            prize_level=lottery_code.prize_level,
            user_name=receive_data.user_name,
            mobile=receive_data.mobile,
            address=receive_data.address,
            status=0,  # 默认待发货
            image_url=receive_data.image_url
        )
        
        db.add(prize_receive_info)
        logger.info(f"创建领奖信息成功: Code={receive_data.code}, User={receive_data.user_name}")
        lottery_code.is_used = True
        lottery_code.ip = ip_address
        lottery_code.scan_time = datetime.now()
        await db.commit()
        await db.refresh(prize_receive_info)
        await db.refresh(lottery_code)
        return prize_receive_info
    
    @staticmethod
    async def get_lottery_code_by_code(db: AsyncSession, code: str) -> Optional[LotteryCode]:
        """根据代码获取抽奖码信息"""
        result = await db.execute(
            select(LotteryCode).where(LotteryCode.code == code)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_by_id(db: AsyncSession, receive_id: int) -> Optional[PrizeReceiveInfo]:
        """根据ID获取领奖信息"""
        result = await db.execute(
            select(PrizeReceiveInfo).where(PrizeReceiveInfo.id == receive_id)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def get_by_code(db: AsyncSession, code: str) -> Optional[PrizeReceiveInfo]:
        """根据中奖码获取领奖信息"""
        result = await db.execute(
            select(PrizeReceiveInfo).where(PrizeReceiveInfo.code == code)
        )
        return result.scalar_one_or_none()
    
    @staticmethod
    async def update_prize_receive_info(
        db: AsyncSession,
        receive_id: int,
        update_data: PrizeReceiveInfoUpdate
    ) -> Optional[PrizeReceiveInfo]:
        """更新领奖信息"""
        
        prize_receive_info = await PrizeReceiveService.get_by_id(db, receive_id)
        if not prize_receive_info:
            raise NotFoundException("领奖信息不存在")
        
        # 更新字段
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(prize_receive_info, field, value)
        
        await db.commit()
        await db.refresh(prize_receive_info)
        
        logger.info(f"更新领奖信息成功: ID={receive_id}")
        return prize_receive_info
    
    @staticmethod
    async def ship_prize(db: AsyncSession, receive_id: int) -> PrizeReceiveInfo:
        """发货"""
        
        prize_receive_info = await PrizeReceiveService.get_by_id(db, receive_id)
        if not prize_receive_info:
            raise NotFoundException("领奖信息不存在")
        
        if not prize_receive_info.can_ship():
            raise ValidationException("当前状态不允许发货或信息不完整")
        
        prize_receive_info.status = 1  # 已发货
        await db.commit()
        await db.refresh(prize_receive_info)
        
        logger.info(f"发货成功: ID={receive_id}, Code={prize_receive_info.code}")
        return prize_receive_info
    
    @staticmethod
    async def confirm_receipt(db: AsyncSession, receive_id: int) -> PrizeReceiveInfo:
        """确认收货"""
        
        prize_receive_info = await PrizeReceiveService.get_by_id(db, receive_id)
        if not prize_receive_info:
            raise NotFoundException("领奖信息不存在")
        
        if not prize_receive_info.can_confirm_receipt():
            raise ValidationException("当前状态不允许确认收货")
        
        prize_receive_info.status = 2  # 已收货
        await db.commit()
        await db.refresh(prize_receive_info)
        
        logger.info(f"确认收货成功: ID={receive_id}, Code={prize_receive_info.code}")
        return prize_receive_info
    
    @staticmethod
    async def get_prize_receive_list(
        db: AsyncSession,
        query: PrizeReceiveInfoQuery,
        page: int = 1,
        size: int = 20
    ) -> Tuple[List[PrizeReceiveInfo], int]:
        """获取领奖信息列表"""
        
        # 构建查询条件
        conditions = []
        
        if query.code:
            conditions.append(PrizeReceiveInfo.code.like(f"%{query.code}%"))
        
        if query.prize_level is not None:
            conditions.append(PrizeReceiveInfo.prize_level == query.prize_level)
        
        if query.status is not None:
            conditions.append(PrizeReceiveInfo.status == query.status)
        
        if query.user_name:
            conditions.append(PrizeReceiveInfo.user_name.like(f"%{query.user_name}%"))
        
        if query.mobile:
            conditions.append(PrizeReceiveInfo.mobile.like(f"%{query.mobile}%"))
        
        if query.start_date:
            conditions.append(PrizeReceiveInfo.created_at >= query.start_date)
        
        if query.end_date:
            conditions.append(PrizeReceiveInfo.created_at <= query.end_date)
        
        # 构建基础查询
        base_query = select(PrizeReceiveInfo)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count()).select_from(
            base_query.subquery()
        )
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        offset = (page - 1) * size
        items_query = base_query.order_by(desc(PrizeReceiveInfo.created_at)).offset(offset).limit(size)
        items_result = await db.execute(items_query)
        items = items_result.scalars().all()
        
        return list(items), total
    
    @staticmethod
    async def get_statistics(db: AsyncSession) -> Dict[str, Any]:
        """获取领奖统计信息"""
        
        # 总中奖数
        total_result = await db.execute(
            select(func.count()).select_from(PrizeReceiveInfo)
        )
        total_prizes = total_result.scalar()
        
        # 各状态统计
        status_result = await db.execute(
            select(
                PrizeReceiveInfo.status,
                func.count().label('count')
            ).group_by(PrizeReceiveInfo.status)
        )
        status_stats = {row.status: row.count for row in status_result}
        
        # 各等级统计
        level_result = await db.execute(
            select(
                PrizeReceiveInfo.prize_level,
                func.count().label('count')
            ).group_by(PrizeReceiveInfo.prize_level)
        )
        prize_level_stats = {row.prize_level: row.count for row in level_result}
        
        return {
            "total_prizes": total_prizes,
            "pending_count": status_stats.get(0, 0),
            "shipped_count": status_stats.get(1, 0),
            "received_count": status_stats.get(2, 0),
            "prize_level_stats": prize_level_stats,
            "status_stats": status_stats
        }
    
    @staticmethod
    async def delete_prize_receive_info(db: AsyncSession, receive_id: int) -> bool:
        """删除领奖信息"""
        
        prize_receive_info = await PrizeReceiveService.get_by_id(db, receive_id)
        if not prize_receive_info:
            raise NotFoundException("领奖信息不存在")
        
        await db.delete(prize_receive_info)
        await db.commit()
        
        logger.info(f"删除领奖信息成功: ID={receive_id}")
        return True
