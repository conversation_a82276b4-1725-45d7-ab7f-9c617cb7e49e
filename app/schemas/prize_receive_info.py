from typing import Optional, List
from pydantic import BaseModel, Field, field_validator
from datetime import datetime
import re


class PrizeReceiveInfoBase(BaseModel):
    """领奖信息基础模型"""
    code: str = Field(..., max_length=128, description="中奖code")
    prize_level: int = Field(default=0, ge=0, description="中奖等级")
    user_name: Optional[str] = Field(None, max_length=64, description="姓名")
    mobile: Optional[str] = Field(None, max_length=64, description="手机号")
    address: Optional[str] = Field(None, max_length=255, description="地址")
    status: int = Field(default=0, ge=0, le=2, description="状态（0待发货，1已发货，2已收货）")
    image_url: Optional[str] = Field(None, max_length=512, description="四等奖壁纸链接")
    
    @field_validator('mobile')
    def validate_mobile(cls, v):
        if v and not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确')
        return v
    
    @field_validator('code')
    def validate_code(cls, v):
        # 验证是否为32位字符串（UUID去除-后的格式）
        if not re.match(r'^[a-f0-9]{32}$', v.lower()):
            raise ValueError('中奖码格式不正确，应为32位十六进制字符串')
        return v.lower()


class PrizeReceiveInfoCreate(PrizeReceiveInfoBase):
    """创建领奖信息模型"""
    user_name: str = Field(..., min_length=1, max_length=64, description="姓名")
    mobile: str = Field(..., description="手机号")
    address: str = Field(..., min_length=1, max_length=255, description="地址")


class PrizeReceiveInfoUpdate(BaseModel):
    """更新领奖信息模型"""
    user_name: Optional[str] = Field(None, max_length=64, description="姓名")
    mobile: Optional[str] = Field(None, max_length=64, description="手机号")
    address: Optional[str] = Field(None, max_length=255, description="地址")
    status: Optional[int] = Field(None, ge=0, le=2, description="状态")
    image_url: Optional[str] = Field(None, max_length=512, description="四等奖壁纸链接")
    
    @field_validator('mobile')
    def validate_mobile(cls, v):
        if v and not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确')
        return v


class PrizeReceiveInfoResponse(PrizeReceiveInfoBase):
    """领奖信息响应模型"""
    id: int = Field(..., description="雪花ID")
    status_name: str = Field(..., description="状态名称")
    prize_name: str = Field(..., description="奖品名称")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    class Config:
        from_attributes = True


class PrizeReceiveInfoList(BaseModel):
    """领奖信息列表响应模型"""
    items: List[PrizeReceiveInfoResponse] = Field(..., description="领奖信息列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class PrizeReceiveInfoStats(BaseModel):
    """领奖信息统计模型"""
    total_prizes: int = Field(..., description="总中奖数")
    pending_count: int = Field(..., description="待发货数量")
    shipped_count: int = Field(..., description="已发货数量")
    received_count: int = Field(..., description="已收货数量")
    prize_level_stats: dict = Field(..., description="各等级中奖统计")
    status_stats: dict = Field(..., description="状态统计")


class ShipmentUpdate(BaseModel):
    """发货更新模型"""
    status: int = Field(1, description="状态（1已发货）")
    
    @field_validator('status')
    def validate_status(cls, v):
        if v != 1:
            raise ValueError('发货状态必须为1')
        return v


class ReceiptConfirm(BaseModel):
    """确认收货模型"""
    status: int = Field(2, description="状态（2已收货）")
    
    @field_validator('status')
    def validate_status(cls, v):
        if v != 2:
            raise ValueError('收货状态必须为2')
        return v


class PrizeReceiveInfoQuery(BaseModel):
    """领奖信息查询模型"""
    code: Optional[str] = Field(None, description="中奖码")
    prize_level: Optional[int] = Field(None, ge=0, description="中奖等级")
    status: Optional[int] = Field(None, ge=0, le=2, description="状态")
    user_name: Optional[str] = Field(None, description="姓名")
    mobile: Optional[str] = Field(None, description="手机号")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    
    @field_validator('mobile')
    def validate_mobile(cls, v):
        if v and not re.match(r'^1[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确')
        return v
