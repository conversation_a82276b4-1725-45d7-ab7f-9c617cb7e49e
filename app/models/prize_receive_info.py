from sqlalchemy import Column, BigInteger, Integer, String, DateTime
from sqlalchemy.sql import func
from .base import BaseModel


class PrizeReceiveInfo(BaseModel):
    """领奖信息模型"""
    __tablename__ = "prize_receive_info"
    
    # 重写id字段为BigInteger（雪花ID）
    id = Column(BigInteger, primary_key=True, comment="雪花ID")
    code = Column(String(128), nullable=False, index=True, comment="中奖code")
    prize_level = Column(Integer, default=0, nullable=False, comment="中奖等级")
    user_name = Column(String(64), nullable=True, comment="姓名")
    mobile = Column(String(64), nullable=True, comment="手机号")
    address = Column(String(255), nullable=True, comment="地址")
    status = Column(Integer, default=0, nullable=False, comment="状态（0待发货，1已发货，2已收货）")
    image_url = Column(String(512), nullable=True, comment="四等奖壁纸链接")
    
    # 重写时间字段名以匹配数据库
    created_at = Column(DateTime, default=func.now(), nullable=True, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=True, comment="更新时间")
    
    def __repr__(self):
        return f"<PrizeReceiveInfo(id={self.id}, code='{self.code}', prize_level={self.prize_level}, status={self.status})>"
    
    def to_dict(self, exclude: set = None) -> dict:
        """转换为字典，重写以处理特殊字段"""
        exclude = exclude or set()
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude:
                value = getattr(self, column.name)
                if hasattr(value, 'isoformat'):  # datetime对象
                    value = value.isoformat()
                result[column.name] = value
        
        return result
    
    @property
    def status_name(self) -> str:
        """获取状态名称"""
        status_map = {
            0: "待发货",
            1: "已发货", 
            2: "已收货"
        }
        return status_map.get(self.status, "未知状态")
    
    @property
    def prize_name(self) -> str:
        """获取奖品名称"""
        from app.services.lottery_code_service import LotteryCodeService
        return LotteryCodeService.get_prize_name(self.prize_level)
    
    def is_pending(self) -> bool:
        """是否待发货"""
        return self.status == 0
    
    def is_shipped(self) -> bool:
        """是否已发货"""
        return self.status == 1
    
    def is_received(self) -> bool:
        """是否已收货"""
        return self.status == 2
    
    def can_ship(self) -> bool:
        """是否可以发货"""
        return self.status == 0 and self.user_name and self.mobile and self.address
    
    def can_confirm_receipt(self) -> bool:
        """是否可以确认收货"""
        return self.status == 1
