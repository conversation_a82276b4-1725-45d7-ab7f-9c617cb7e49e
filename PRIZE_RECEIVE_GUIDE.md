# 领奖信息系统使用指南

## 🎯 系统概述

领奖信息系统用于管理中奖用户的收货信息和发货状态，包含完整的领奖流程：提交信息 → 发货 → 确认收货。

## 📋 数据库表结构

```sql
CREATE TABLE prize_receive_info (
    id          BIGINT        NOT NULL PRIMARY KEY,     -- 雪花ID
    code        VARCHAR(128)  NOT NULL,                 -- 中奖code
    prize_level INT DEFAULT 0 NOT NULL,                 -- 中奖等级
    user_name   VARCHAR(64)   NULL,                     -- 姓名
    mobile      VARCHAR(64)   NULL,                     -- 手机号
    address     VARCHAR(255)  NULL,                     -- 地址
    status      INT DEFAULT 0 NOT NULL,                 -- 状态（0待发货，1已发货，2已收货）
    image_url   VARCHAR(512)  NULL,                     -- 四等奖壁纸链接
    created_at  DATETIME      NULL,                     -- 创建时间
    updated_at  DATETIME      NULL                      -- 更新时间
);

CREATE INDEX prize_receive_info_code_index ON prize_receive_info (code);
```

## 🏗️ 系统架构

```
用户扫码中奖 → 填写领奖信息 → 管理员发货 → 用户确认收货
     ↓              ↓              ↓            ↓
  lottery页面    claim页面      管理后台      用户确认
```

## 🚀 API 接口

### 基础路径
```
/api/v1/prize-receive/
```

### 1. 创建领奖信息
```http
POST /api/v1/prize-receive/
Content-Type: application/json

{
    "code": "a1b2c3d4e5f6789012345678901234ab",
    "user_name": "张三",
    "mobile": "13800138000",
    "address": "北京市朝阳区某某街道某某小区某某号"
}
```

**响应示例：**
```json
{
    "code": 201,
    "message": "领奖信息提交成功",
    "data": {
        "id": 321206064299249664,
        "code": "a1b2c3d4e5f6789012345678901234ab",
        "prize_level": 1,
        "user_name": "张三",
        "mobile": "13800138000",
        "address": "北京市朝阳区某某街道某某小区某某号",
        "status": 0,
        "status_name": "待发货",
        "prize_name": "一等奖",
        "created_at": "2025-06-05T16:45:00"
    }
}
```

### 2. 获取领奖信息
```http
GET /api/v1/prize-receive/{receive_id}
GET /api/v1/prize-receive/code/{code}
```

### 3. 更新领奖信息
```http
PUT /api/v1/prize-receive/{receive_id}
Content-Type: application/json

{
    "address": "新的收货地址"
}
```

### 4. 发货操作
```http
POST /api/v1/prize-receive/{receive_id}/ship
```

### 5. 确认收货
```http
POST /api/v1/prize-receive/{receive_id}/confirm
```

### 6. 获取列表
```http
GET /api/v1/prize-receive/?page=1&size=20&status=0&prize_level=1
```

**查询参数：**
- `page`: 页码（默认1）
- `size`: 每页大小（默认20，最大100）
- `code`: 中奖码（模糊搜索）
- `prize_level`: 中奖等级筛选
- `status`: 状态筛选（0待发货，1已发货，2已收货）
- `user_name`: 姓名（模糊搜索）
- `mobile`: 手机号（模糊搜索）

### 7. 统计信息
```http
GET /api/v1/prize-receive/stats/overview
```

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "total_prizes": 150,
        "pending_count": 45,
        "shipped_count": 80,
        "received_count": 25,
        "prize_level_stats": {
            "1": 50,
            "2": 30,
            "3": 20
        },
        "status_stats": {
            "0": 45,
            "1": 80,
            "2": 25
        }
    }
}
```

## 🎮 前端页面

### 1. 领奖页面
```
http://localhost:8000/claim?code=a1b2c3d4e5f6789012345678901234ab
```

**功能特性：**
- ✅ 响应式设计，适配移动端
- ✅ 表单验证（姓名、手机号、地址）
- ✅ 实时提交状态反馈
- ✅ 错误处理和重试机制
- ✅ 无障碍访问支持

### 2. 页面流程
1. **用户扫码中奖** → 抽奖页面显示"立即领取"按钮
2. **点击领取** → 跳转到领奖页面 `/claim?code=xxx`
3. **填写信息** → 姓名、手机号、收货地址
4. **提交成功** → 显示成功页面，等待发货

## 🔧 状态管理

### 状态定义
- **0 - 待发货**：用户已提交信息，等待发货
- **1 - 已发货**：管理员已发货，等待用户确认
- **2 - 已收货**：用户已确认收货，流程完成

### 状态转换
```
待发货(0) → [管理员发货] → 已发货(1) → [用户确认] → 已收货(2)
```

### 业务规则
1. **创建限制**：
   - 中奖码必须存在且已扫码验证
   - 中奖等级必须大于0（非"谢谢参与"）
   - 每个中奖码只能创建一次领奖信息

2. **发货条件**：
   - 状态为"待发货"
   - 收货信息完整（姓名、手机号、地址）

3. **确认收货条件**：
   - 状态为"已发货"

## 🧪 测试指南

### 1. 运行测试脚本
```bash
python test_prize_receive.py
```

### 2. 手动测试流程
1. **创建中奖抽奖码**：
   ```bash
   curl -X POST "http://localhost:8000/api/v1/lottery-codes/" \
     -H "Content-Type: application/json" \
     -d '{"url": "http://localhost:8000/lottery?code=test", "prize_level": 1}'
   ```

2. **扫码验证**：
   ```bash
   curl -X POST "http://localhost:8000/api/v1/lottery-codes/scan" \
     -H "Content-Type: application/json" \
     -d '{"code": "生成的32位代码"}'
   ```

3. **访问领奖页面**：
   ```
   http://localhost:8000/claim?code=生成的32位代码
   ```

4. **填写并提交领奖信息**

5. **测试管理功能**：
   - 查看领奖列表
   - 发货操作
   - 确认收货

### 3. 错误测试
- 无效中奖码
- 未中奖的代码
- 重复提交
- 无效手机号格式
- 空字段验证

## 📱 移动端适配

领奖页面已完全适配移动端：
- ✅ 响应式布局
- ✅ 触摸友好的表单控件
- ✅ 移动端键盘优化
- ✅ 手机号输入自动过滤
- ✅ 地址输入多行支持

## 🔒 安全特性

1. **输入验证**：
   - 中奖码格式验证（32位十六进制）
   - 手机号格式验证（中国大陆手机号）
   - 字段长度限制

2. **业务验证**：
   - 中奖码存在性验证
   - 中奖状态验证
   - 重复提交防护

3. **数据安全**：
   - SQL注入防护
   - XSS防护
   - 敏感信息脱敏

## 🚀 部署说明

1. **数据库迁移**：确保 `prize_receive_info` 表已创建
2. **API 服务**：FastAPI 服务包含所有接口
3. **前端页面**：`/claim` 路由已配置
4. **权限控制**：根据需要添加管理员权限验证

## 📊 监控指标

建议监控的关键指标：
- 领奖信息提交成功率
- 各状态的数量分布
- 平均发货时间
- 用户确认收货率
- API 响应时间

## 🎉 完整流程示例

1. **用户扫码中奖** → 获得中奖码 `abc123...`
2. **点击立即领取** → 跳转 `/claim?code=abc123...`
3. **填写收货信息** → 提交到 API
4. **管理员发货** → 调用发货接口
5. **用户确认收货** → 完成整个流程

系统现在已经完全可用，支持完整的中奖领奖流程！
