#!/usr/bin/env python3
"""
测试领奖信息 API
"""

import requests
import json
import sys

def test_prize_receive_api():
    """测试领奖信息 API"""
    
    base_url = "http://localhost:8000"
    
    # 首先创建一个中奖的抽奖码
    print("=== 创建中奖抽奖码 ===")
    create_response = requests.post(
        f"{base_url}/api/v1/lottery-codes/",
        json={
            "url": f"{base_url}/lottery?code=test",
            "prize_level": 1  # 一等奖
        }
    )
    
    if create_response.status_code != 201:
        print(f"创建抽奖码失败: {create_response.text}")
        return
    
    create_data = create_response.json()
    test_code = create_data["data"]["code"]
    print(f"创建成功，中奖码: {test_code}")
    
    # 扫码验证（模拟中奖）
    print(f"\n=== 扫码验证 ===")
    scan_response = requests.post(
        f"{base_url}/api/v1/lottery-codes/scan",
        json={"code": test_code}
    )
    
    if scan_response.status_code != 200:
        print(f"扫码失败: {scan_response.text}")
        return
    
    scan_data = scan_response.json()
    print(f"扫码结果: {json.dumps(scan_data, indent=2, ensure_ascii=False)}")
    
    # 测试创建领奖信息
    print(f"\n=== 创建领奖信息 ===")
    receive_data = {
        "code": test_code,
        "user_name": "张三",
        "mobile": "13800138000",
        "address": "北京市朝阳区某某街道某某小区某某号"
    }
    
    receive_response = requests.post(
        f"{base_url}/api/v1/prize-receive/",
        json=receive_data
    )
    
    print(f"创建领奖信息状态: {receive_response.status_code}")
    receive_result = receive_response.json()
    print(f"创建结果: {json.dumps(receive_result, indent=2, ensure_ascii=False)}")
    
    if receive_response.status_code != 201:
        return
    
    receive_id = receive_result["data"]["id"]
    
    # 测试获取领奖信息
    print(f"\n=== 获取领奖信息 ===")
    get_response = requests.get(f"{base_url}/api/v1/prize-receive/{receive_id}")
    get_result = get_response.json()
    print(f"获取结果: {json.dumps(get_result, indent=2, ensure_ascii=False)}")
    
    # 测试根据中奖码获取领奖信息
    print(f"\n=== 根据中奖码获取领奖信息 ===")
    get_by_code_response = requests.get(f"{base_url}/api/v1/prize-receive/code/{test_code}")
    get_by_code_result = get_by_code_response.json()
    print(f"获取结果: {json.dumps(get_by_code_result, indent=2, ensure_ascii=False)}")
    
    # 测试更新领奖信息
    print(f"\n=== 更新领奖信息 ===")
    update_data = {
        "address": "上海市浦东新区某某路某某号"
    }
    update_response = requests.put(
        f"{base_url}/api/v1/prize-receive/{receive_id}",
        json=update_data
    )
    update_result = update_response.json()
    print(f"更新结果: {json.dumps(update_result, indent=2, ensure_ascii=False)}")
    
    # 测试发货
    print(f"\n=== 测试发货 ===")
    ship_response = requests.post(f"{base_url}/api/v1/prize-receive/{receive_id}/ship")
    ship_result = ship_response.json()
    print(f"发货结果: {json.dumps(ship_result, indent=2, ensure_ascii=False)}")
    
    # 测试确认收货
    print(f"\n=== 测试确认收货 ===")
    confirm_response = requests.post(f"{base_url}/api/v1/prize-receive/{receive_id}/confirm")
    confirm_result = confirm_response.json()
    print(f"确认收货结果: {json.dumps(confirm_result, indent=2, ensure_ascii=False)}")
    
    # 测试获取列表
    print(f"\n=== 获取领奖信息列表 ===")
    list_response = requests.get(f"{base_url}/api/v1/prize-receive/?page=1&size=10")
    list_result = list_response.json()
    print(f"列表结果: {json.dumps(list_result, indent=2, ensure_ascii=False)}")
    
    # 测试统计信息
    print(f"\n=== 获取统计信息 ===")
    stats_response = requests.get(f"{base_url}/api/v1/prize-receive/stats/overview")
    stats_result = stats_response.json()
    print(f"统计结果: {json.dumps(stats_result, indent=2, ensure_ascii=False)}")

def test_error_cases():
    """测试错误情况"""
    
    base_url = "http://localhost:8000"
    
    print("\n=== 测试错误情况 ===")
    
    # 测试无效中奖码
    print("1. 测试无效中奖码")
    invalid_response = requests.post(
        f"{base_url}/api/v1/prize-receive/",
        json={
            "code": "invalid_code_123",
            "user_name": "测试用户",
            "mobile": "13800138000",
            "address": "测试地址"
        }
    )
    print(f"无效中奖码结果: {invalid_response.status_code} - {invalid_response.json()}")
    
    # 测试无效手机号
    print("\n2. 测试无效手机号")
    invalid_mobile_response = requests.post(
        f"{base_url}/api/v1/prize-receive/",
        json={
            "code": "a1b2c3d4e5f6789012345678901234ab",
            "user_name": "测试用户",
            "mobile": "123456",  # 无效手机号
            "address": "测试地址"
        }
    )
    print(f"无效手机号结果: {invalid_mobile_response.status_code} - {invalid_mobile_response.json()}")

def test_pages():
    """测试页面访问"""
    
    base_url = "http://localhost:8000"
    
    print("\n=== 测试页面访问 ===")
    
    pages = [
        ("/claim", "领奖页面"),
        ("/claim?code=a1b2c3d4e5f6789012345678901234ab", "带参数的领奖页面")
    ]
    
    for path, name in pages:
        try:
            response = requests.get(f"{base_url}{path}")
            print(f"{name}: {response.status_code}")
            if response.status_code == 200:
                print(f"  Content-Type: {response.headers.get('content-type')}")
                print(f"  Content-Length: {len(response.content)} bytes")
        except Exception as e:
            print(f"{name}: 错误 - {e}")

if __name__ == "__main__":
    print("开始测试领奖信息 API...")
    
    try:
        test_prize_receive_api()
        test_error_cases()
        test_pages()
        print("\n=== 测试完成 ===")
    except Exception as e:
        print(f"测试失败: {e}")
        sys.exit(1)
